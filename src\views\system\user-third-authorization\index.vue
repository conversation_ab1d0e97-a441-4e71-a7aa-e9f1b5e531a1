<template>
  <div class="tree-table-page">
    <div class="tree-table-tree-panel">
      <!-- v-if="treeOptions.dataSource.length" -->
      <TreeGeneral
        style="width: 220px"
        ref="treeGeneralRef"
        :treeOptions="treeOptions"
        @onTreeMounted="onTreeMounted"
        @select="clickTreeNode"
      />
    </div>
    <div class="tree-table-right-panel">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="姓名">
          <a-input v-model="queryParam.name" placeholder="请输入姓名" allow-clear @keyup.enter.native="handleQuery" />
        </a-form-item>

        <a-form-item label="关键字">
          <a-input
            v-model="queryParam.keywords"
            placeholder="请输入用户名/手机号码"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <a-form-item label="状态">
          <a-select placeholder="请选择状态" v-model="queryParam.enabled" style="width: 100%" allow-clear>
            <a-select-option v-for="(d, index) in statusOptions" :key="index" :value="d.dictKey">
              {{ d.dictValue }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="身份证号">
          <a-input
            v-model="queryParam.idNo"
            placeholder="请输入身份证号"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <template #table>
          <!-- :otherHeight="30" -->
          <VxeTable
            ref="vxeTableRef"
            :tableTitle="tableTitle"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @selectChange="selectChange"
            @handlePageChange="handlePageChange"
          >
            <div class="table-operations" slot="button">
              <a-button type="danger" v-if="isChecked" @click="handleDelete">
                <a-icon type="delete" />
                删除
              </a-button>
            </div>
          </VxeTable>
        </template>
      </VxeTableForm>
      <!-- 修改密码抽屉 -->
      <reset-password v-if="showResetPassword" ref="resetPassword" @close="showResetPassword = false" />
      <!-- 修改头像 showUpdateAvatar -->
      <UpdateAvatar v-if="showUpdateAvatar" ref="updateAvatar" @close="showUpdateAvatar = false" />
      <select-user selectModel="multi" v-model="selectedUser" v-show="false" ref="selectUserRef" />

      <!-- 授权弹窗 -->
      <a-modal
        title="用户授权"
        :visible="showAuthorizeModal"
        @ok="handleAuthorizeConfirm"
        @cancel="showAuthorizeModal = false"
        :confirmLoading="false"
      >
        <div>
          <p>请选择您的授权信息：</p>
          <a-select
            v-model="authorizeForm.authInfo"
            placeholder="请选择或输入授权信息"
            style="width: 100%"
            :dropdownMatchSelectWidth="false"
            mode="combobox"
            :filterOption="false"
          >
            <a-select-option v-if="currentUser" :value="currentUser.name">
              {{ currentUser.name }}
            </a-select-option>
            <a-select-option v-if="currentUser" :value="currentUser.username">
              {{ currentUser.username }}
            </a-select-option>
            <a-select-option v-if="currentUser" :value="currentUser.mobile">
              {{ currentUser.mobile }}
            </a-select-option>
            <a-select-option v-if="currentUser" :value="currentUser.email">
              {{ currentUser.email }}
            </a-select-option>
          </a-select>
        </div>
      </a-modal>

      <!-- 取消授权弹窗 -->
      <a-modal
        title="取消授权"
        :visible="showCancelAuthorizeModal"
        @ok="handleCancelAuthorizeConfirm"
        @cancel="showCancelAuthorizeModal = false"
        :confirmLoading="false"
      >
        <div>
          <p>确认要取消对该用户的授权吗？</p>
          <p v-if="currentUser">用户：{{ currentUser.name }} ({{ currentUser.username }})</p>
        </div>
      </a-modal>
    </div>
  </div>
</template>

<script>
  import {
    listUser,
    delUser,
    userStatus,
    resetRandomPwd,
    bindDingUser,
    unbindDingUser,
    listUserThirdAuth,
    updateThirdAuth
  } from '@/api/system/user'
  import { listRole } from '@/api/system/role'
  import { deptTree, getDeptTree } from '@/api/system/dept'
  import { listPost } from '@/api/system/post'
  import ResetPassword from '../user/modules/ResetPassword'
  import UpdateAvatar from '../user/modules/UpdateAvatar'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import Split from '@/components/pt/split/Index'
  import SelectUser from '@/components/pt/selectUser/SelectDingUser'
  import moment from 'moment'
  import TreeGeneral from '@/components/TreeGeneral'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'

  export default {
    name: 'UserThirdAuthorization',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      ResetPassword,
      UpdateAvatar,
      AdvanceTable,
      Split,
      SelectUser,
    },
    data() {
      return {
        treeOptions: {
          getDataApi: getDeptTree,
          // dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'deptName',
            key: 'id',
            value: 'deptId',
          },
        },
        isChecked: false,
        showResetPassword: false,
        showUpdateAvatar: false,
        showAuthorizeModal: false,
        showCancelAuthorizeModal: false,
        currentUser: null,
        authorizeForm: {
          authInfo: '',
        },

        list: [],
        colorList: ['#F38709', '#813AFD', '#00C4AA', '#4B7AEE'],
        sexOptions: [],
        educationOptions: [],
        workExperienceOptions: [],
        tableTitle: '用户三方授权',
        selectedRowKeys: [],
        selectedRows: [],
        // 高级搜索 展开/关闭
        advanced: false,
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        ids: [],
        names: [],
        userNames: [],
        expandedKeys: [],
        selectedUser: '',
        loading: false,

        total: 0,
        // 状态数据字典
        statusOptions: [],
        deptCheckedValue: '',
        // 部门树选项
        deptOptions: [],
        roleOptions: [], //角色下拉选项
        postOptions: [], //岗位下拉选项
        bindUserId: '',
        // 日期范围
        dateRange: [],
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        queryParam: {
          sort: [],
          pageNum: 1,
          deptId: 0,
          idNo: '',
          enabled: '',
          keywords: '',
          name: '',
          pageSize: 10,
        },
        columns: [
          { type: 'checkbox', width: 30, fixed: 'left' },
          {
            title: '姓名',
            field: 'name',
            width: 120,
            maxWidth: 120,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <div>
                    {row.name}
                  </div>
                )
              },
            },
            fixed: 'left',
          },
          {
            title: '用户名',
            field: 'username',
            width: 100,
            maxWidth: 100,
            showOverflow: 'tooltip',
            fixed: 'left',
          },
          {
            title: '手机号',
            field: 'mobile',
            width: 120,
            maxWidth: 120,
            showOverflow: 'tooltip',
          },
          {
            title: '邮箱',
            field: 'email',
            width: 160,
            maxWidth: 160,
            showOverflow: 'tooltip',
          },
          {
            title: '性别',
            field: 'sex',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.sexOptions.find(entry => entry.dictKey == row.sex)?.dictValue
              },
            },
            width: 60,
          },
          {
            title: '部门',
            field: 'deptName',
            width: 200,
            maxWidth: 220,
            showOverflow: 'tooltip',
          },
          {
            title: '角色',
            field: 'roleNames',
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return row.roleNames ? (
                  row.roleNames.includes(',') ? (
                    row.roleNames.split(',').map(item => <a-tag color='blue'>{item}</a-tag>)
                  ) : (
                    <div>
                      <a-tag color='blue'>{row.roleNames}</a-tag>
                    </div>
                  )
                ) : (
                  <div></div>
                )
              },
            },
            width: 270,
            maxWidth: 270,
            showOverflow: 'tooltip',
          },
          {
            title: '岗位',
            field: 'postNames',
            slots: {
              default: ({ row, rowIndex }) => {
                return row.postNames ? (
                  row.postNames.includes(',') ? (
                    row.postNames.split(',').map(item => <a-tag color='blue'>{item}</a-tag>)
                  ) : (
                    <div>
                      <a-tag color='blue'>{row.postNames}</a-tag>
                    </div>
                  )
                ) : (
                  <div></div>
                )
              },
            },
            width: 270,
            maxWidth: 270,
            showOverflow: 'tooltip',
          },
          {
            title: '用户状态',
            field: 'enabled',
            fixed: 'right',
            width: 80,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <a-switch
                    style={{ backgroundColor: row.enabled ? '#52c41a' : '#fc011a' }}
                    size='small'
                    checked-children='正常'
                    un-checked-children='停用'
                    checked={row.enabled}
                    disabled={true}
                  ></a-switch>
                )
              },
            },
          },
          {
            title: '操作',
            field: 'operation',
            width: 100,
            align: 'center',
            fixed: 'right',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    {Number(row.enabled) === 0 ? (
                      <a onClick={() => this.handleAuthorize(row)}>授权</a>
                    ) : (
                      <a onClick={() => this.handleCancelAuthorize(row)}>取消授权</a>
                    )}
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    filters: {},
    created() {
      this.getList()
      this.getTreeselect()
      this.statusOptions = [
        {
            "dictDataId": 24,
            "dictCode": "isDisabled",
            "dictKey": "1",
            "dictValue": "启用",
            "option1": null,
            "option2": null,
            "option3": null,
            "option4": null,
            "option5": null,
            "remark": null,
            "sort": 10,
            "isDisabled": 0,
            "createdTime": "2023-01-31 10:53:24"
        },
        {
            "dictDataId": 25,
            "dictCode": "isDisabled",
            "dictKey": "0",
            "dictValue": "停用",
            "option1": null,
            "option2": null,
            "option3": null,
            "option4": null,
            "option5": null,
            "remark": null,
            "sort": 20,
            "isDisabled": 0,
            "createdTime": "2023-01-31 10:53:35"
        }
    ]
    //   this.getDicts('isDisabled').then(response => {
    //     this.statusOptions = response.data
    //   })
      this.getDicts('sex').then(response => {
        this.sexOptions = response.data
      })
      this.getDicts('education').then(response => {
        this.educationOptions = response.data
      })
      this.getDicts('workExperience').then(response => {
        this.workExperienceOptions = response.data
      })
    },
    computed: {},
    watch: {
      multiple: {
        deep: true,
        handler: function (newValue, oldValue) {
          // console.log('multiple changed:', this.multiple, newValue, oldValue)
        },
      },
      selectedUser(val) {
        let ddUnionId = val.unionIds
        let ddUserId = val.userIds
        if (!ddUnionId) {
          return
        } else if (!ddUserId) {
          return
        }
        bindDingUser(this.bindUserId, ddUnionId, ddUserId).then(response => {
          this.getList()
          this.$message.success('当前用户钉钉绑定成功', 3)
        })
      },
    },
    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = !pageSize ? 9 : pageSize
        this.getList()
      },
      // 树加载完成后
      onTreeMounted(data) {
        this.tableTitle = data[0].name
        this.queryParam.deptId = data[0].key //data[0].key.substr(1, data[0].key.length)
        this.getList()
      },
      clickTreeNode(node) {
        const key = node.$options.propsData.dataRef.key
        this.queryParam.deptId = key //key.substr(1, key.length)
        this.tableTitle = node.$options.propsData.dataRef.title

        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 查询部门下拉树结构 */
      getTreeselect() {
        deptTree('0').then(response => {
          this.deptOptions = this.changeIconState(response.data)
        })
        listRole().then(response => {
          this.roleOptions = response.data
        })
        listPost().then(response => {
          this.postOptions = response.data
        })
      },
      changeIconState(data) {
        for (let i in data) {
          data[i].slots = {
            icon: data[i].type == 0 ? 'org' : data[i].type == 1 ? 'company' : data[i].type == 2 ? 'dept' : '',
          }
          if (data[i].children) {
            this.changeIconState(data[i].children)
          }
        }
        return data
      },
      getExpandedKeys(nodes, expandLevel) {
        if (expandLevel > 1) {
          // 最后一层不展开
          nodes.forEach(node => {
            this.expandedKeys.push(node.id)
            expandLevel = expandLevel - 1
            return this.getExpandedKeys(node.children, expandLevel)
          })
        }
      },
      statusFormat(row) {
        return this.selectDictLabel(this.statusOptions, row.status)
      },
      /** 查询定时任务列表 */
      getList() {
        this.loading = true
        this.expandedKeys.push(0)
        listUserThirdAuth(this.queryParam).then(response => {
          this.list = response.data.data?.map(item => ({
            ...item,
            enabled: item.enabled === 0 ? false : true,
          }))
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = []
        this.queryParam = {
          pageNum: 1,
          deptId: 0,
          idNo: '',
          enabled: '',
          keywords: '',
          name: '',
          pageSize: 10,
          sort: [],
        }
        this.handleQuery()
      },

      //分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.userId)
        this.names = valObj.records.map(item => item.name)
        this.isChecked = !!valObj.records.length
      },
      toggleAdvanced() {
        this.advanced = !this.advanced
      },
      clickDeptNode(node) {
        this.queryParam.deptId = node.$options.propsData.eventKey
        this.deptCheckedValue = node.$options.propsData.eventKey
        this.tableTitle = node.$options.propsData.label
        this.getList()
      },
      sexFormat(row) {
        return this.selectDictLabel(this.sexOptions, row.sex)
      },
      /* 授权操作 */
      handleAuthorize(record) {
        this.currentUser = record
        this.authorizeForm.authInfo = ''
        this.showAuthorizeModal = true
      },
      /* 取消授权操作 */
      handleCancelAuthorize(record) {
        this.currentUser = record
        this.showCancelAuthorizeModal = true
      },
      /* 确认授权 */
      handleAuthorizeConfirm() {
        if (!this.authorizeForm.authInfo) {
          this.$message.warning('请输入授权信息')
          return
        }

        // 这里可以调用授权API
        console.log('授权用户:', this.currentUser)
        console.log('授权信息:', this.authorizeForm.authInfo)

        this.$message.success('授权成功')
        this.showAuthorizeModal = false
        this.getList() // 刷新列表
      },
      /* 确认取消授权 */
      handleCancelAuthorizeConfirm() {
        // 这里可以调用取消授权API
        console.log('取消授权用户:', this.currentUser)

        this.$message.success('取消授权成功')
        this.showCancelAuthorizeModal = false
        this.getList() // 刷新列表
      },
      /* 用户密码重置 */
      handleResetPwd(record) {
        this.showResetPassword = true
        this.$nextTick(() => this.$refs.resetPassword.handleResetPwd(record))
      },
      handleUpdateAvatar(record) {
        this.showUpdateAvatar = true
        this.$nextTick(() => this.$refs.updateAvatar.handleUpdateAvatar(record))
      },
      handleRandomPwd(row) {
        var that = this
        const userIds = row.userId || this.ids
        const userNames = row.name || this.userNames

        this.$confirm({
          title: '确认重置所选中密码数据?',
          content: '当前选中名称为"' + userNames + '"的数据',
          onOk() {
            return resetRandomPwd(userIds).then(() => {
              that.getList()
              that.$message.success('重置成功', 3)
            })
          },
          onCancel() {},
        })
      },
      /** 更新用户钉钉绑定状态 (0-绑定 1-解绑)**/
      onChangeDDStatus(e, record) {
        var that = this
        const userId = record.userId
        const username = record.name
        that.bindUserId = record.userId
        let recordIsDisabled = record.ddUserId || record.ddUnionId ? 1 : 2
        let isDisabledName = recordIsDisabled == 1 ? '解绑' : '绑定'

        if (recordIsDisabled == 1) {
          this.$confirm({
            title: '是否"' + isDisabledName + '"所选中数据?',
            content: '当前选中的数据:' + username,
            onOk() {
              return unbindDingUser(userId).then(res => {
                that.getList()
                that.$message.success('当前用户钉钉解绑成功！', 5)
              })
            },
            onCancel() {},
          })
        } else if (recordIsDisabled == 2) {
          this.$nextTick(() => this.$refs.selectUserRef.showSelectUser())
        }
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const userIds = row.userId || this.ids
        const userNames = row.name || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + userNames + '"的数据',
          onOk() {
            return delUser(userIds).then(() => {
              that.selectedRowKeys = []
              that.multiple = true
              that.$message.success('删除成功', 3)
              that.getList()
            })
          },
          onCancel() {},
        })
      },

      getRandomColor(index) {
        return this.colorList[index % 4]
      },
      getShowName(name) {
        if (name.length > 2) {
          name = name.substring(name.length - 2)
        }
        return name
      },
      onClickRow(record, index) {
        // 移除了修改功能，这里可以留空或者添加其他逻辑
      },
      setDataOptionInfo(treeDataOption) {
        this.deptOptions = treeDataOption
      },
      handleTableChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped src="./custom.less">
/deep/ .ant-switch-loading, .ant-switch-disabled {
    opacity: 1 !important;
}
</style>