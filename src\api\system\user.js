import request from '@/utils/request'
import { praseStrEmpty } from '@/utils/aidex'

// 查询用户列表
export function listUser(data) {
  return request({
    url: '/sys/user/page',
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  })
}

// 查询用户详情
export function getUser(userId) {
  let data= userId ? '&userId=' + userId : ''
  return request({
    url: '/sys/user/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/sys/user/add',
    method: 'post',
    data: data,
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/sys/user/update',
    method: 'post',
    data: data,
  })
}

// 删除用户
export function delUser(userIds) {
  let data= userIds ? 'userIds=' + userIds : ''
  return request({
    url: '/sys/user/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}


// 用户状态修改:启用-停用
export function userStatus(isDisabled, userId) {
  let data = 'isDisabled=' + isDisabled
  data += userId ? '&userId=' + userId : ''
  return request({
    url: '/sys/user/switch',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

// 管理员用户密码重置
export function resetUserPwd(userId, newPassword) {
  let data = 'newPassword=' + newPassword
  data += userId ? '&userId=' + userId : ''
  return request({
    url: '/sys/user/resetPassword',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  
  })
}

// 管理员重置用户随机密码
export function resetRandomPwd(userId) {
  let data =  userId ? '&userId=' + userId : ''
  return request({
    url: '/sys/user/randomPassword',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  let data = 'newPassword=' + newPassword
  data += oldPassword ? '&oldPassword=' + oldPassword : ''
  return request({
    url: '/sys/user/changePassword',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  
  })
}

// 校验用户名称唯一性
export function checkUserNameUnique(username) {
  let data =  'username=' + username 
  return request({
    url: '/sys/user/isUsernameUnique',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}
// 校验用户手机号唯一性
export function checkPhoneUnique(mobile,userId) {
  let data = 'mobile=' + mobile
  data += '&userId=' + userId 
  return request({
    url: '/sys/user/isMobileUnique',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  })
}

// 同步钉钉用户
export function syncDingUser(deptId){
  let data = 'deptId='+ deptId
  return request({
    url:'/sys/user/dingtalk/sync',
    method:'post',
    headers:{
      'Content-Type':'application/x-www-form-urlencoded',
    },
    data:data,
  })
}


// 获取登录用户应用权限
export function getAppPermissions() {
  return request({
    url: '/sys/user/getAppPermissions',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
// 获取登录用户菜单权限
export function getMenuPermissions() {
  return request({
    url: '/sys/user/getMenuPermissions',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 钉钉企业通讯录
export function dingUser() {
  return request({
    url: '/external/dingtalk/addressBook',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 绑定钉钉用户
export function bindDingUser(userId,ddUnionId,ddUserId){
  let data = 'userId='+ userId
  data +='&ddUnionId='+ ddUnionId
  data +='&ddUserId='+ ddUserId
  return request({
    url:'/sys/user/dingtalk/bind',
    method:'post',
    headers:{
      'Content-Type':'application/x-www-form-urlencoded',
    },
    data:data,
  })
}

// 解绑钉钉用户
export function unbindDingUser(userId){
  let data = 'userId='+ userId
  return request({
    url:'/sys/user/dingtalk/unbind',
    method:'post',
    headers:{
      'Content-Type':'application/x-www-form-urlencoded',
    },
    data:data,
  })
}

//用户第三方授权分页查用户列表
export function listUserThirdAuth(data) {
  return request({
    url: '/sys/thirdAuth/page',
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  })
}

//授权--取消授权
export function updateThirdAuth(data) {
  return request({
    url: '/sys/thirdAuth/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data:data
  })
}


