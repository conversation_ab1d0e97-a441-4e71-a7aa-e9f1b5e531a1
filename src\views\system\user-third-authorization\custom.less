.tree-table-page {
  display: flex;
  .tree-table-tree-panel {
    // background: linear-gradient(180deg, rgba(55, 114, 255, 0.2) 1%, rgba(55, 114, 255, 0) 15%);
    border-right: 1px solid #f2f3f5;
    background-color: #f7f8fa;
    height: calc(100vh - 120px);
    border-radius: 8px 0 0 8px;
    padding: 16px 10px;

    .tree-panel-tree-box {
      margin: 12px 0 7px;
      overflow: auto;
      height: calc(100% - 38px);
    }
    .tab-tree-panel-box {
      margin: 12px 0 7px;
      overflow: auto;
      height: calc(100vh - 230px);
    }
    .ant-tabs .ant-tabs-bar {
      margin: 0 0 12px 10px;
      border-bottom: none;
      .ant-tabs-nav .ant-tabs-tab {
        padding: 0 0 8px;
        margin: 0 16px 0 0;
        font-weight: 700;
      }
    }
    .ant-tree.ant-tree-show-line li span.ant-tree-switcher {
      background: transparent;
    }
  }
  .tree-table-tree-panel-modal {
    background: linear-gradient(180deg, rgba(55, 114, 255, 0.2) 1%, rgba(55, 114, 255, 0) 15%);
    border-right: 1px solid #f2f3f5;
    height: 100%;
    .tree-panel-tree-box {
      height: calc(100% - 30px);
    }
    .tab-tree-panel-box {
      height: calc(100% - 207px);
    }
  }
  .tree-table-right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-radius: 0 8px 8px 0;
  }
}
/deep/ .ant-switch-loading, .ant-switch-disabled {
    opacity: 1 !important;
}